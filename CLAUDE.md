# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Node.js backend service built with Hono framework for handling WeChat Mini Program (抖音) message subscriptions and scheduled tasks. The project features a modern architecture with Kafka message queuing, Redis-based distributed locking, MySQL database with Drizzle ORM, and comprehensive task scheduling capabilities.

## Essential Commands

### Development
```bash
# Development server with hot reload (Windows users - sets UTF-8 encoding)
pnpm dev

# Start SSH tunnel for remote MySQL/Redis/Kafka access
pnpm run ssh:tunnel

# Build TypeScript to JavaScript
pnpm run build

# Start production server (requires build first)
pnpm start
```

### Database Operations
```bash
# Check database connection and schema
pnpm run db:check

# Open Drizzle Studio for database management
pnpm run db:studio

# Run database migrations
pnpm run db:migrate:last-executed
```

### Code Quality
```bash
# Run ESLint
pnpm lint

# Fix ESLint issues automatically
pnpm run lint:fix
```

### Docker
```bash
# Build Docker image with cache
pnpm run docker:build-cache

# Build Docker image without cache
pnpm run docker:build

# Save Docker image to tar file
pnpm run docker:save
```

## Architecture Overview

### Core Framework Stack
- **Web Framework**: Hono with OpenAPI support (`@hono/zod-openapi`)
- **Database**: MySQL with Drizzle ORM
- **Message Queue**: Apache Kafka for task distribution
- **Cache/Lock**: Redis for distributed locking and caching
- **Logging**: Pino with structured logging and multiple output targets
- **Validation**: Zod schemas with OpenAPI integration

### Key Directories Structure
```
src/
├── app.ts              # Main application routes and handlers
├── index.ts            # Server entry point with HTTPS/HTTP support
├── schema.ts           # Database schema definitions (Drizzle)
├── lib/                # Core utilities and configurations
│   ├── create-app.ts   # Hono app factory with middleware setup
│   ├── logger.ts       # Pino logging configuration with specialized loggers
│   └── types.ts        # TypeScript type definitions
├── services/           # Business logic and external integrations
│   ├── kafka*.ts       # Kafka producer/consumer/scheduler services
│   ├── scheduler.ts    # Traditional direct task scheduler
│   ├── distributed-lock.ts # Redis-based distributed locking
│   ├── douyin-message.ts   # WeChat Mini Program message service
│   └── task-handlers.ts    # Task execution handlers
├── middlewares/        # Custom Hono middlewares
├── routes/             # Route modules (currently message-tasks)
└── types/              # Type definitions for domain models
```

### Dual Scheduler Architecture

The project implements two scheduling modes that can be switched via `SCHEDULER_MODE`:

1. **Kafka Mode** (`SCHEDULER_MODE=kafka`, recommended):
   - Scheduler publishes tasks to Kafka topics
   - Separate consumers process tasks asynchronously
   - Better for distributed systems and reliability
   - Supports horizontal scaling

2. **Direct Mode** (`SCHEDULER_MODE=direct`):
   - Traditional in-process task execution
   - Lower latency but less scalable
   - Fallback option when Kafka is unavailable

### Distributed Lock System
Uses Redis-based distributed locks with environment isolation:
- Lock keys include environment prefix (e.g., `development_scheduler_lock:`)
- Prevents multiple instances from executing same scheduled task
- Configurable via `INSTANCE_ID` and `ENVIRONMENT` variables

## Key Environment Variables

### Required
- `DATABASE_URL`: MySQL connection string
- `SSH_HOST`, `SSH_USER`, `SSH_PASS`: SSH tunnel configuration for remote services

### Scheduler Configuration
- `SCHEDULER_MODE`: `kafka`|`direct`|`disabled` (default: `kafka`)
- `SCHEDULER_ENABLED`: Enable/disable scheduler (default: `true`)
- `SCHEDULER_MAIN_INSTANCE`: Mark as main instance for scheduling (default: `true`)
- `INSTANCE_ID`: Unique instance identifier for distributed locking

### Kafka Configuration
- `KAFKA_ENABLED`: Enable Kafka integration (default: `true`)
- `KAFKA_REQUIRED`: Fail startup if Kafka unavailable (default: `false`)
- `KAFKA_BOOTSTRAP_SERVERS`: Kafka broker addresses (default: `127.0.0.1:9092`)

## Database Schema

The database uses Drizzle ORM with these main tables:
- `event_data`: Event tracking (supports date-based partitioning)
- `xthy_user_data`: User profile and engagement data
- `nqhy_subscribe`: User subscription preferences
- `nqhy_message_tasks`: Scheduled task configurations with JSON message data

## Logging System

Specialized loggers available via `@/lib/logger`:
- `logger`: General application logger
- `apiLogger`: API request/response logging
- `redisLogger`: Redis operations
- `dbLogger`: Database operations
- `schedulerLogger`: Scheduled task execution
- `douyinLogger`: WeChat Mini Program service calls
- `sshLogger`: SSH tunnel operations

## Development Notes

### Path Aliases
- `@/*` maps to `src/*` (configured in tsconfig.json)

### Code Style
- Uses @antfu/eslint-config with single quotes and 2-space indentation
- TypeScript strict mode disabled for flexibility
- Console logging allowed (overrides default eslint rule)

### SSL/HTTPS Support
- Automatic SSL certificate detection in `certs/` directory
- Supports multiple certificate file naming conventions
- Falls back to HTTP if SSL setup fails

### Windows Development
- UTF-8 encoding automatically set via `chcp 65001` in dev script
- SSH tunnel and dev scripts optimized for Windows PowerShell