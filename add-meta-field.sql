-- 添加 meta 字段到 nqhy_message_tasks 表
-- 用于存储任务的元数据配置信息，JSON格式

-- 1. 添加 meta 字段
ALTER TABLE `nqhy_message_tasks` 
ADD COLUMN `meta` JSON NULL 
COMMENT '元数据，JSON格式，包含任务的额外配置信息';

-- 2. 可选：为现有的单次任务设置 meta.type
-- 将现有 type='single' 的任务迁移到 meta.type='single'
UPDATE `nqhy_message_tasks` 
SET `meta` = JSON_OBJECT('type', 'single')
WHERE `type` = 'single' AND (`meta` IS NULL OR JSON_EXTRACT(`meta`, '$.type') IS NULL);

-- 3. 验证字段是否添加成功
-- DESCRIBE `nqhy_message_tasks`;

-- 4. 查看数据示例
-- SELECT id, tpl_id, type, meta 
-- FROM `nqhy_message_tasks` 
-- LIMIT 5;

-- 5. 查看单次任务的 meta 配置
-- SELECT id, tpl_id, type, meta
-- FROM `nqhy_message_tasks` 
-- WHERE JSON_EXTRACT(`meta`, '$.type') = 'single';

-- 6. 示例：创建一个带有 meta 配置的单次任务
-- INSERT INTO `nqhy_message_tasks` (
--   tpl_id, platform, app_id, max, cron_expression, 
--   message_data, interval_hours, enabled, type, meta
-- ) VALUES (
--   'single_meta_test_001', 'dy', 'your_app_id', 1, '0 10 * * *',
--   '[{"title": "单次通知", "content": "这是一个使用meta配置的单次任务"}]',
--   24, true, 'recurring', 
--   JSON_OBJECT('type', 'single', 'priority', 'high', 'category', 'promotion')
-- );
