-- 添加 type 字段到 nqhy_message_tasks 表
-- 用于区分任务类型：recurring(循环任务) 或 single(单次任务)

-- 1. 添加 type 字段
ALTER TABLE `nqhy_message_tasks` 
ADD COLUMN `type` VARCHAR(20) NOT NULL DEFAULT 'recurring' 
COMMENT '任务类型：recurring(循环任务) 或 single(单次任务)';

-- 2. 验证字段是否添加成功
-- DESCRIBE `nqhy_message_tasks`;

-- 3. 查看数据
-- SELECT id, tpl_id, type, enabled 
-- FROM `nqhy_message_tasks` 
-- LIMIT 5;

-- 4. 可选：创建一个单次任务示例
-- INSERT INTO `nqhy_message_tasks` (
--   tpl_id, platform, app_id, max, cron_expression, 
--   message_data, interval_hours, enabled, type
-- ) VALUES (
--   'single_test_001', 'dy', 'your_app_id', 1, '0 10 * * *',
--   '[{"title": "单次通知", "content": "这是一个单次任务"}]',
--   24, true, 'single'
-- );
