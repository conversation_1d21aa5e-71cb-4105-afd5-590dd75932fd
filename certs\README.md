# SSL证书文件

请将你的SSL证书文件放在这个目录下：

## 文件要求

- `server.crt` - SSL证书文件
- `server.key` - SSL私钥文件

## 文件权限

确保证书文件有正确的权限：

```bash
chmod 600 server.key
chmod 644 server.crt
```

## 证书格式

支持的证书格式：
- PEM格式（推荐）
- CRT格式

## 注意事项

1. 私钥文件(`server.key`)包含敏感信息，请妥善保管
2. 不要将私钥文件提交到版本控制系统
3. 证书文件会以只读模式挂载到Docker容器中

## 测试证书

如果需要生成测试用的自签名证书：

```bash
# 生成私钥
openssl genrsa -out server.key 2048

# 生成证书
openssl req -new -x509 -key server.key -out server.crt -days 365 \
  -subj "/C=CN/ST=Beijing/L=Beijing/O=NQHY/CN=localhost"
```
