@echo off
chcp 65001 >nul 2>&1
echo Starting NQHY Backend deployment...

REM Check if Docker is installed
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Docker is not installed, please install Docker Desktop first
    pause
    exit /b 1
)

docker-compose --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Docker Compose is not installed, please install Docker Compose first
    pause
    exit /b 1
)

REM Check environment file
if not exist ".env.production.local" (
    echo WARNING: .env.production.local file not found
    echo Please copy .env.production to .env.production.local and fill in real configuration

    if not exist ".env.production" (
        echo ERROR: .env.production file does not exist, please check project files
        pause
        exit /b 1
    )

    echo Creating default .env.production.local...
    copy .env.production .env.production.local >nul
    echo Created .env.production.local, please edit this file to fill in real configuration
    echo Run this script again after editing
    pause
    exit /b 0
)

REM Create logs directory
echo Creating logs directory...
if not exist "logs" mkdir logs

REM Stop existing containers
echo Stopping existing containers...
docker-compose down --remove-orphans >nul 2>&1

REM Ask if cleanup old images
set /p cleanup="Clean up old Docker images? (y/N): "
if /i "%cleanup%"=="y" (
    echo Cleaning up old images...
    docker system prune -f >nul 2>&1
    docker image prune -f >nul 2>&1
)

REM Build new image
echo Building Docker image...
docker-compose build --no-cache

if %errorlevel% neq 0 (
    echo ERROR: Image build failed
    pause
    exit /b 1
)

REM Start services
echo Starting services...
docker-compose --env-file .env.production.local up -d

if %errorlevel% neq 0 (
    echo ERROR: Service startup failed
    pause
    exit /b 1
)

REM Wait for service to start
echo Waiting for service to start...
timeout /t 10 /nobreak >nul

REM Check service status
echo Checking service status...
docker-compose ps

echo Service deployment completed
echo.
echo SUCCESS: Deployment completed!
echo Service information:
echo    - Application URL: http://localhost:3000
echo    - Container name: nqhy-backend
echo.
echo Common commands:
echo    View logs: docker-compose logs -f
echo    Restart service: docker-compose restart
echo    Stop service: docker-compose down
echo    Enter container: docker exec -it nqhy-backend sh
echo.
echo Monitoring commands:
echo    docker stats nqhy-backend
echo    docker-compose ps

pause
