services:
  kafka-ui:
    image: provectuslabs/kafka-ui:latest
    container_name: kafka-ui
    ports:
      - "8080:8080"
    environment:
      # Kafka集群配置
      KAFKA_CLUSTERS_0_NAME: "nqhy-kafka"
      KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: "host.docker.internal:9092"

      # SASL认证配置
      KAFKA_CLUSTERS_0_PROPERTIES_SECURITY_PROTOCOL: "SASL_PLAINTEXT"
      KAFKA_CLUSTERS_0_PROPERTIES_SASL_MECHANISM: "PLAIN"
      KAFKA_CLUSTERS_0_PROPERTIES_SASL_JAAS_CONFIG: 'org.apache.kafka.common.security.plain.PlainLoginModule required username="csdnxthy" password="2024@Xthy@888";'

      # UI配置
      DYNAMIC_CONFIG_ENABLED: "true"
      AUTH_TYPE: "DISABLED"

    restart: unless-stopped

    # 网络配置，确保能访问到本地的SSH隧道
    extra_hosts:
      - "host.docker.internal:host-gateway"
