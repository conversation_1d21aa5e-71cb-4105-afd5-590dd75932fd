services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    image: nqhy-backend:latest
    container_name: nqhy-backend
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      # 基础配置
      NODE_ENV: production
      PORT: 3000
      ENVIRONMENT: production
      TZ: Asia/Shanghai

      # 日志配置
      LOG_LEVEL: info
      LOG_FORMAT: json

      # 数据库配置（通过SSH隧道连接）
      DATABASE_URL: mysql://xthy007:saf354%40%21110@127.0.0.1:3306/xthy007

      # Redis配置
      REDIS_PASSWORD: cnmb@Gunsb!
      REDIS_HOST: redis
      REDIS_PORT: 6379

      # # SSH隧道配置
      # SSH_HOST: *************
      # SSH_PORT: 22
      # SSH_USER: root
      # SSH_PASS: ${SSH_PASSWORD} # 从环境变量读取

      # Kafka配置
      KAFKA_ENABLED: true
      KAFKA_REQUIRED: false
      KAFKA_BOOTSTRAP_SERVERS: kafka:9092
      KAFKA_USERNAME: admin
      KAFKA_PASSWORD: 12345678
      KAFKA_GROUP_ID: nqhy-backend-group

      # 定时任务配置
      SCHEDULER_ENABLED: true
      SCHEDULER_MAIN_INSTANCE: true
      SCHEDULER_MODE: direct
      INSTANCE_ID: instance-1

    volumes:
      # 日志持久化
      - ./logs:/app/logs
      # SSH密钥（如果使用密钥认证）
      # - ~/.ssh:/home/<USER>/.ssh:ro

    networks:
      - nqhy-network

    depends_on:
      - redis
      - kafka

    # 健康检查
    # healthcheck:
    #   test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
    #   interval: 30s
    #   timeout: 10s
    #   retries: 3
    #   start_period: 30s

    # 资源限制
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: "0.5"
        reservations:
          memory: 256M
          cpus: "0.25"

  # Redis 服务
  redis:
    image: redis:7-alpine
    container_name: nqhy-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    command: redis-server --requirepass "cnmb@Gunsb!"
    volumes:
      - redis_data:/data
    networks:
      - nqhy-network
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: "0.25"

  # Zookeeper 服务 (Kafka 依赖)
  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    container_name: nqhy-zookeeper
    restart: unless-stopped
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    volumes:
      - zookeeper_data:/var/lib/zookeeper/data
      - zookeeper_logs:/var/lib/zookeeper/log
    networks:
      - nqhy-network
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: "0.25"

  # Kafka 服务
  kafka:
    image: confluentinc/cp-kafka:7.4.0
    container_name: nqhy-kafka
    restart: unless-stopped
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
      - "9101:9101"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092,PLAINTEXT_HOST://localhost:9092
      KAFKA_LISTENERS: PLAINTEXT://0.0.0.0:29092,PLAINTEXT_HOST://0.0.0.0:9092
      KAFKA_INTER_BROKER_LISTENER_NAME: PLAINTEXT
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_JMX_PORT: 9101
      KAFKA_JMX_HOSTNAME: localhost
      # SASL 认证配置
      KAFKA_SASL_ENABLED_MECHANISMS: PLAIN
      KAFKA_SASL_MECHANISM_INTER_BROKER_PROTOCOL: PLAIN
      KAFKA_LISTENER_NAME_PLAINTEXT_SASL_ENABLED_MECHANISMS: PLAIN
    volumes:
      - kafka_data:/var/lib/kafka/data
    networks:
      - nqhy-network
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: "0.5"

volumes:
  redis_data:
    driver: local
  zookeeper_data:
    driver: local
  zookeeper_logs:
    driver: local
  kafka_data:
    driver: local

networks:
  nqhy-network:
    driver: bridge
