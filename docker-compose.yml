services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    image: nqhy-backend:latest
    container_name: nqhy-backend
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      # 基础配置
      NODE_ENV: production
      PORT: 3000
      ENVIRONMENT: production
      TZ: Asia/Shanghai

      # 日志配置
      LOG_LEVEL: info
      LOG_FORMAT: json

      # 数据库配置（通过SSH隧道连接）
      DATABASE_URL: mysql://xthy007:saf354%40%21110@127.0.0.1:3306/xthy007

      # Redis配置
      REDIS_PASSWORD: cnmb@Gunsb!
      REDIS_HOST: 127.0.0.1
      REDIS_PORT: 6379

      # # SSH隧道配置
      # SSH_HOST: *************
      # SSH_PORT: 22
      # SSH_USER: root
      # SSH_PASS: ${SSH_PASSWORD} # 从环境变量读取

      # Kafka配置
      KAFKA_ENABLED: false
      KAFKA_REQUIRED: false
      KAFKA_BOOTSTRAP_SERVERS: 127.0.0.1:9092
      KAFKA_USERNAME: admin
      KAFKA_PASSWORD: 12345678
      KAFKA_GROUP_ID: nqhy-backend-group

      # 定时任务配置
      SCHEDULER_ENABLED: true
      SCHEDULER_MAIN_INSTANCE: true
      SCHEDULER_MODE: direct
      INSTANCE_ID: instance-1

    volumes:
      # 日志持久化
      - ./logs:/app/logs
      # SSH密钥（如果使用密钥认证）
      # - ~/.ssh:/home/<USER>/.ssh:ro

    networks:
      - nqhy-network

    # 健康检查
    # healthcheck:
    #   test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
    #   interval: 30s
    #   timeout: 10s
    #   retries: 3
    #   start_period: 30s

    # 资源限制
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: "0.5"
        reservations:
          memory: 256M
          cpus: "0.25"

networks:
  nqhy-network:
    driver: bridge
