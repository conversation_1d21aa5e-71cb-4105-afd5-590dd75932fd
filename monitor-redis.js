#!/usr/bin/env node

/**
 * Redis连接监控脚本
 * 持续监控Redis连接状态和分布式锁
 */

import { getRedis } from './src/redis.ts'
import { distributedLock } from './src/services/distributed-lock.ts'

console.log('🔍 启动Redis连接监控...\n')

let redis = null
let monitorInterval = null

// 监控Redis连接状态
async function monitorRedisConnection() {
  try {
    if (!redis) {
      redis = await getRedis()
    }

    // 测试连接
    const start = Date.now()
    const pong = await redis.ping()
    const latency = Date.now() - start

    console.log(`✅ Redis连接正常 - 延迟: ${latency}ms - 响应: ${pong}`)

    // 获取Redis信息
    const info = await redis.info('server')
    const lines = info.split('\r\n')
    const uptimeSeconds = lines.find(line => line.startsWith('uptime_in_seconds:'))

    if (uptimeSeconds) {
      const uptime = Number.parseInt(uptimeSeconds.split(':')[1])
      const hours = Math.floor(uptime / 3600)
      const minutes = Math.floor((uptime % 3600) / 60)
      console.log(`📊 Redis运行时间: ${hours}小时${minutes}分钟`)
    }
  }
  catch (error) {
    console.error(`❌ Redis连接失败: ${error.message}`)

    // 重置连接
    redis = null

    // 尝试重新连接
    try {
      console.log('🔄 尝试重新连接Redis...')
      redis = await getRedis()
      console.log('✅ Redis重连成功')
    }
    catch (retryError) {
      console.error(`❌ Redis重连失败: ${retryError.message}`)
    }
  }
}

// 监控分布式锁状态
async function monitorDistributedLocks() {
  try {
    if (!redis) {
      redis = await getRedis()
    }

    // 获取所有锁键
    const lockKeys = await redis.keys('*_scheduler_lock:*')

    if (lockKeys.length > 0) {
      console.log(`🔒 当前活跃的分布式锁: ${lockKeys.length}个`)

      for (const key of lockKeys.slice(0, 5)) { // 只显示前5个
        const ttl = await redis.ttl(key)
        const value = await redis.get(key)

        console.log(`   - ${key}: TTL=${ttl}s, 持有者=${value?.substring(0, 20)}...`)
      }

      if (lockKeys.length > 5) {
        console.log(`   ... 还有 ${lockKeys.length - 5} 个锁`)
      }
    }
    else {
      console.log('🔓 当前没有活跃的分布式锁')
    }
  }
  catch (error) {
    console.error(`❌ 监控分布式锁失败: ${error.message}`)
  }
}

// 测试分布式锁功能
async function testDistributedLock() {
  try {
    const testLockKey = 'monitor_test_lock'
    const instanceId = `monitor_${Date.now()}`

    console.log('🧪 测试分布式锁功能...')

    // 获取锁
    const acquired = await distributedLock.acquireLock(testLockKey, 30, instanceId)

    if (acquired) {
      console.log('✅ 成功获取测试锁')

      // 等待2秒
      await new Promise(resolve => setTimeout(resolve, 2000))

      // 释放锁
      const released = await distributedLock.releaseLock(testLockKey, instanceId)

      if (released) {
        console.log('✅ 成功释放测试锁')
      }
      else {
        console.log('⚠️ 释放测试锁失败')
      }
    }
    else {
      console.log('⚠️ 获取测试锁失败（可能被其他实例持有）')
    }
  }
  catch (error) {
    console.error(`❌ 测试分布式锁失败: ${error.message}`)
  }
}

// 显示Redis统计信息
async function showRedisStats() {
  try {
    if (!redis) {
      redis = await getRedis()
    }

    const info = await redis.info('stats')
    const lines = info.split('\r\n')

    const totalConnections = lines.find(line => line.startsWith('total_connections_received:'))
    const totalCommands = lines.find(line => line.startsWith('total_commands_processed:'))
    const keyspaceHits = lines.find(line => line.startsWith('keyspace_hits:'))
    const keyspaceMisses = lines.find(line => line.startsWith('keyspace_misses:'))

    console.log('📈 Redis统计信息:')
    if (totalConnections)
      console.log(`   总连接数: ${totalConnections.split(':')[1]}`)
    if (totalCommands)
      console.log(`   总命令数: ${totalCommands.split(':')[1]}`)
    if (keyspaceHits)
      console.log(`   缓存命中: ${keyspaceHits.split(':')[1]}`)
    if (keyspaceMisses)
      console.log(`   缓存未命中: ${keyspaceMisses.split(':')[1]}`)
  }
  catch (error) {
    console.error(`❌ 获取Redis统计信息失败: ${error.message}`)
  }
}

// 主监控循环
async function startMonitoring() {
  console.log('🚀 开始监控Redis连接和分布式锁...')
  console.log('⏹️  按 Ctrl+C 停止监控\n')

  let cycle = 0

  monitorInterval = setInterval(async () => {
    cycle++
    console.log(`\n=== 监控周期 ${cycle} (${new Date().toLocaleTimeString()}) ===`)

    // 基础连接监控
    await monitorRedisConnection()

    // 分布式锁监控
    await monitorDistributedLocks()

    // 每5个周期显示一次统计信息
    if (cycle % 5 === 0) {
      await showRedisStats()
    }

    // 每10个周期测试一次分布式锁
    if (cycle % 10 === 0) {
      await testDistributedLock()
    }
  }, 10000) // 每10秒监控一次
}

// 优雅关闭
process.on('SIGINT', async () => {
  console.log('\n🛑 收到停止信号，正在关闭监控...')

  if (monitorInterval) {
    clearInterval(monitorInterval)
  }

  if (redis) {
    try {
      await redis.quit()
      console.log('✅ Redis连接已关闭')
    }
    catch (error) {
      console.error('❌ 关闭Redis连接失败:', error.message)
    }
  }

  console.log('👋 监控已停止')
  process.exit(0)
})

// 启动监控
startMonitoring().catch((error) => {
  console.error('❌ 启动监控失败:', error.message)
  process.exit(1)
})
