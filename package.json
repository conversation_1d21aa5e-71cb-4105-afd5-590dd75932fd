{"name": "nqhy-backend", "version": "1.0.0", "main": "index.js", "packageManager": "pnpm@8.15.0", "type": "module", "scripts": {"dev": "chcp 65001 && tsx watch src/index.ts", "start": "node dist/index.js", "start:ts": "node -r tsconfig-paths/register -r tsx/cjs src/index.ts", "build": "npx tsc", "lint": "eslint .", "ssh:tunnel": "node src/ssh-tunnel.js", "db:check": "tsx src/scripts/check-database.ts", "db:studio": "drizzle-kit studio", "db:migrate:last-executed": "tsx src/scripts/migrate-add-last-executed-at.ts", "lint:fix": "eslint \"src/**/*.{js,ts}\" --fix", "docker:build": "docker-compose build --no-cache", "docker:build-cache": "docker-compose build", "docker:save": "docker save nqhy-backend:latest -o nqhy-backend.tar", "docker:start": "docker-compose up -d", "docker:stop": "docker-compose down", "docker:restart": "docker-compose restart", "docker:logs": "docker-compose logs -f", "docker:status": "docker-compose ps"}, "pnpm": {"peerDependencyRules": {"ignoreMissing": ["typescript"]}, "overrides": {"eslint": "^8.57.0"}}, "engines": {"node": ">=16.0.0", "pnpm": ">=8.0.0"}, "keywords": ["hono", "drizzle", "mysql", "backend", "typescript", "ssh-tunnel"], "author": "", "license": "ISC", "description": "", "dependencies": {"@hono/node-server": "^1.17.1", "@hono/zod-openapi": "^1.0.2", "@scalar/hono-api-reference": "^0.9.12", "dayjs": "^1.11.13", "dotenv": "^17.2.0", "drizzle-orm": "^0.44.3", "drizzle-zod": "^0.8.2", "hono": "^4.8.5", "hono-pino": "^0.10.1", "kafkajs": "^2.2.4", "mysql2": "^3.14.2", "pino": "^9.7.0", "pino-pretty": "^13.0.0", "redis": "^5.6.1", "ssh2": "^1.16.0", "stoker": "^1.4.3", "zod": "^4.0.8"}, "devDependencies": {"@antfu/eslint-config": "^4.18.0", "@eslint/css": "^0.10.0", "@eslint/js": "^9.31.0", "@eslint/json": "^0.13.1", "@eslint/markdown": "^7.1.0", "@types/node": "^24.1.0", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "drizzle-kit": "^0.31.4", "eslint": "8.57.1", "eslint-config-google": "^0.14.0", "eslint-plugin-import": "^2.32.0", "eslint-plugin-react": "^7.37.5", "globals": "^16.3.0", "tsconfig-paths": "^4.2.0", "tsx": "^4.20.3", "typescript": "^5.8.3", "typescript-eslint": "^8.38.0"}}