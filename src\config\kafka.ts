import type { ConsumerConfig, KafkaConfig, ProducerConfig } from 'kafkajs'

// Kafka连接配置
export const kafkaConfig: KafkaConfig = {
  clientId: 'nqhy-backend',
  brokers: [process.env.KAFKA_BOOTSTRAP_SERVERS || '127.0.0.1:9092'],
  sasl: {
    mechanism: 'plain',
    username: process.env.KAFKA_USERNAME || 'csdnxthy',
    password: process.env.KAFKA_PASSWORD || '2024@Xthy@888',
  },
  ssl: false,
  connectionTimeout: 3000,
  authenticationTimeout: 1000,
  reauthenticationThreshold: 10000,
  requestTimeout: 30000,
  enforceRequestTimeout: false,
  retry: {
    initialRetryTime: 100,
    retries: 8,
  },
}

// Producer配置
export const producerConfig: ProducerConfig = {
  maxInFlightRequests: 1,
  idempotent: false,
  transactionTimeout: 30000,
  retry: {
    initialRetryTime: 100,
    retries: 3,
  },
}

// Consumer配置
export const consumerConfig: ConsumerConfig = {
  groupId: process.env.KAFKA_GROUP_ID || 'nqhy-backend-group',
  sessionTimeout: 30000,
  rebalanceTimeout: 60000,
  heartbeatInterval: 3000,
  metadataMaxAge: 300000,
  allowAutoTopicCreation: true,
  maxBytesPerPartition: 1048576,
  minBytes: 1,
  maxBytes: 10485760,
  maxWaitTimeInMs: 5000,
  retry: {
    initialRetryTime: 100,
    retries: 8,
  },
}

// 主题配置
export const topics = {
  // DOUYIN_MESSAGE: 'douyin-message',
  // DOUYIN_TOKEN: 'douyin-token',
  // SYSTEM_LOG: 'system-log',
  // USER_ACTION: 'user-action',
  SCHEDULED_TASKS: 'scheduled-tasks',
} as const

export type TopicName = typeof topics[keyof typeof topics]
