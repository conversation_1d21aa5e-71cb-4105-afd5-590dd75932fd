import { drizzle } from 'drizzle-orm/mysql2'
import mysql from 'mysql2/promise'
import * as schema from './schema'

let dbInstance: ReturnType<typeof drizzle> | null = null

export async function getDb() {
  if (!dbInstance) {
    const connection = await mysql.createConnection({
      uri: 'mysql://xthy007:saf354%40%21110@127.0.0.1:3306/xthy007',
    })

    dbInstance = drizzle(connection, { schema, mode: 'default' })
  }
  return dbInstance
}
