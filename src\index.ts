import { serve } from '@hono/node-server'
import { createServer as createHttpsServer } from 'https'
import { readFileSync, existsSync } from 'fs'
import { join } from 'path'

import { logger } from '@/lib/logger'
import app from './app'
import { startKafkaConsumer, stopKafkaConsumer } from './services/kafka-consumer'
import { kafkaScheduler } from './services/kafka-scheduler'
import { startScheduler } from './services/scheduler'

const port = Number(process.env.PORT) || 3000
const httpsEnabled = process.env.HTTPS_ENABLED !== 'false' 

logger.info('启动应用服务器', {
  port,
  https: httpsEnabled,
  nodeEnv: process.env.NODE_ENV,
  logLevel: process.env.LOG_LEVEL,
})

// 启动定时任务调度器
const kafkaEnabled = process.env.KAFKA_ENABLED === 'true'
const schedulerMode = process.env.SCHEDULER_MODE || 'direct'

switch (schedulerMode) {
  case 'kafka':
    logger.info('使用Kafka模式的定时任务调度器')
    kafkaScheduler.start().catch((error) => {
      logger.error('Kafka调度器启动失败', { error: error.message })
    })
    break

  case 'direct':
    logger.info('使用传统模式的定时任务调度器')
    startScheduler()
    break

  case 'disabled':
    logger.info('定时任务调度器已禁用')
    break

  default:
    logger.warn(`未知的调度器模式: ${schedulerMode}，禁用调度器`)
    break
}

// 启动Kafka消费者
if (kafkaEnabled) {
  startKafkaConsumer().catch((error) => {
    logger.error('启动Kafka消费者失败', { error: error.message })
    // 根据配置决定是否因为Kafka失败而退出应用
    if (process.env.KAFKA_REQUIRED === 'true') {
      process.exit(1)
    }
  })
}

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  logger.fatal('未捕获的异常', { error: error.message, stack: error.stack })
  process.exit(1)
})

process.on('unhandledRejection', (reason, promise) => {
  logger.fatal('未处理的Promise拒绝', { reason, promise })
  process.exit(1)
})

// 优雅关闭
process.on('SIGTERM', async () => {
  logger.info('收到SIGTERM信号，开始优雅关闭')
  try {
    await stopKafkaConsumer()
  }
  catch (error) {
    logger.error('关闭Kafka消费者失败', { error: error.message })
  }
  process.exit(0)
})

process.on('SIGINT', async () => {
  logger.info('收到SIGINT信号，开始优雅关闭')
  try {
    await stopKafkaConsumer()
  }
  catch (error) {
    logger.error('关闭Kafka消费者失败', { error: error.message })
  }
  process.exit(0)
})

// 启动服务器
if (httpsEnabled) {
  // HTTPS模式
  try {
    // 支持多种证书格式
    const certPaths = [
      join(process.cwd(), 'certs', 'server.crt'),
      join(process.cwd(), 'certs', 'server.pem'),
      join(process.cwd(), 'certs', 'cert.pem'),
      join(process.cwd(), 'certs', 'certificate.pem')
    ]

    const keyPaths = [
      join(process.cwd(), 'certs', 'server.key'),
      join(process.cwd(), 'certs', 'private.key'),
      join(process.cwd(), 'certs', 'key.pem'),
      join(process.cwd(), 'certs', 'private.pem')
    ]

    // 查找存在的证书文件
    const certPath = certPaths.find(path => existsSync(path))
    const keyPath = keyPaths.find(path => existsSync(path))

    if (!certPath) {
      throw new Error('SSL证书文件不存在，请确保以下任一文件存在: ' + certPaths.map(p => p.replace(process.cwd(), '.')).join(', '))
    }

    if (!keyPath) {
      throw new Error('SSL私钥文件不存在，请确保以下任一文件存在: ' + keyPaths.map(p => p.replace(process.cwd(), '.')).join(', '))
    }

    logger.info('找到SSL证书文件', {
      certFile: certPath.replace(process.cwd(), '.'),
      keyFile: keyPath.replace(process.cwd(), '.')
    })

    // 读取证书文件
    logger.info('读取SSL证书文件', { certPath, keyPath })

    let certContent, keyContent
    try {
      certContent = readFileSync(certPath, 'utf8')
      keyContent = readFileSync(keyPath, 'utf8')

      logger.info('证书文件读取成功', {
        certLength: certContent.length,
        keyLength: keyContent.length,
        certStart: certContent.substring(0, 50) + '...',
        keyStart: keyContent.substring(0, 50) + '...'
      })
    } catch (readError) {
      throw new Error(`读取证书文件失败: ${readError.message}`)
    }

    const httpsOptions = {
      cert: certContent,
      key: keyContent,
    }

    logger.info('创建HTTPS服务器', { httpsOptionsKeys: Object.keys(httpsOptions) })

    // 使用Hono的serve函数配置HTTPS
    const server = serve({
      fetch: app.fetch,
      port,
      createServer: createHttpsServer,
      serverOptions: httpsOptions
    })

    logger.info('HTTPS服务器启动成功', {
      port,
      protocol: 'https',
      url: `https://localhost:${port}`
    })



  } catch (error) {
    logger.error('HTTPS配置失败', {
      error: error.message,
      stack: error.stack,
      code: error.code,
      errno: error.errno
    })
    logger.info('回退到HTTP模式')

    // 回退到HTTP
    serve({
      fetch: app.fetch,
      port,
    })

    logger.info('HTTP服务器启动成功', {
      port,
      protocol: 'http',
      url: `http://localhost:${port}`
    })
  }
} else {
  // HTTP模式
  serve({
    fetch: app.fetch,
    port,
  })

  logger.info('HTTP服务器启动成功', {
    port,
    protocol: 'http',
    url: `http://localhost:${port}`
  })
}
