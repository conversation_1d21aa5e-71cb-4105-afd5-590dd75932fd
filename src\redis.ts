import { createClient } from 'redis'
import { redisLogger } from '@/lib/logger'

let redisClient: ReturnType<typeof createClient> | null = null

export async function getRedis() {
  if (!redisClient) {
    redisClient = createClient({
      socket: {
        host: '127.0.0.1',
        port: 6379,
      },
      password: process.env.REDIS_PASSWORD,
    })

    redisClient.on('error', (err) => {
      redisLogger.error('Redis连接错误', { error: err.message, code: err.code })
    })

    redisClient.on('connect', () => {
      redisLogger.info('Redis连接成功')
    })

    redisClient.on('ready', () => {
      redisLogger.info('Redis客户端就绪')
    })

    redisClient.on('reconnecting', () => {
      redisLogger.warn('Redis正在重连')
    })

    redisClient.on('end', () => {
      redisLogger.info('Redis连接已关闭')
    })

    await redisClient.connect()
  }
  return redisClient
}

export async function closeRedis() {
  if (redisClient) {
    await redisClient.quit()
    redisClient = null
  }
}

/**
 * 获取抖音access token
 * @param appId 应用ID，如 'tt5e4928f4c142201d02'
 * @returns access token字符串，如果不存在则返回null
 */
export async function getAccessToken(appId: string): Promise<string | null> {
  try {
    const redis = await getRedis()
    const key = `DY_TOKEN_PREFIX:${appId}`
    const value = await redis.get(key)

    if (!value) {
      redisLogger.warn('未找到access token', { appId, key })
      return null
    }

    // 解析JSON字符串
    const tokenData = JSON.parse(value.toString())

    // 提取access_token字段
    if (tokenData && tokenData.access_token) {
      redisLogger.debug('成功获取access token', { appId, tokenLength: tokenData.access_token.length })
      return tokenData.access_token
    }
    else {
      redisLogger.warn('token数据格式不正确', { appId, tokenData })
      return null
    }
  }
  catch (error) {
    redisLogger.error('获取access token失败', { appId, error: error.message })
    return null
  }
}
