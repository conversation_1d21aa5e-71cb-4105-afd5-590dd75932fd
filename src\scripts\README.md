# 数据库迁移脚本

## 概述

这个目录包含数据库迁移脚本，用于更新数据库结构。

## 迁移：添加 lastExecutedAt 字段

### 背景

之前的代码使用 `updatedAt` 字段来判断定时任务的上次执行时间，这不够规范，因为：

1. `updatedAt` 可能因为其他字段的更新而改变
2. 混合了记录更新时间和任务执行时间的概念
3. 不利于数据的准确性和维护

### 解决方案

添加专门的 `lastExecutedAt` 字段来记录任务的上次执行时间：

- **字段名**: `last_executed_at`
- **类型**: `TIMESTAMP NULL`
- **用途**: 专门记录定时任务的上次执行时间
- **与 updatedAt 的区别**: 
  - `updatedAt`: 记录记录本身的最后更新时间
  - `lastExecutedAt`: 记录任务的最后执行时间

### 执行迁移

```bash
# 执行迁移脚本
npm run db:migrate:last-executed

# 或直接运行
npx tsx src/scripts/migrate-add-last-executed-at.ts
```

### 代码更改

1. **schema.ts**: 添加 `lastExecutedAt` 字段定义
2. **douyin-message.ts**: 
   - 使用 `lastExecutedAt` 而不是 `updatedAt` 来判断执行间隔
   - 任务完成后更新 `lastExecutedAt` 字段

### 向后兼容性

- 迁移脚本会将现有的 `updatedAt` 值复制到 `lastExecutedAt`
- 保持现有功能不受影响
- 新字段为可空，不会破坏现有数据

### 验证

迁移完成后，可以通过以下方式验证：

1. 检查表结构：
```sql
DESCRIBE nqhy_message_tasks;
```

2. 检查数据：
```sql
SELECT id, tpl_id, updated_at, last_executed_at 
FROM nqhy_message_tasks 
LIMIT 5;
```

3. 运行定时任务，观察日志中是否使用新字段
