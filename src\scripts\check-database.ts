#!/usr/bin/env tsx

/**
 * 数据库检查和初始化脚本
 */

import { getDb } from '../db'
import { schedulerLogger } from '../lib/logger'

async function checkDatabase() {
  try {
    console.log('🔍 开始检查数据库连接和表结构...')
    
    const db = await getDb()
    
    // 测试数据库连接
    console.log('📡 测试数据库连接...')
    await db.execute('SELECT 1 as test')
    console.log('✅ 数据库连接成功')
    
    // 检查表是否存在
    console.log('📋 检查表结构...')
    
    const tables = await db.execute(`
      SELECT TABLE_NAME 
      FROM information_schema.TABLES 
      WHERE TABLE_SCHEMA = DATABASE() 
      AND TABLE_NAME IN ('nqhy_message_tasks', 'user_subscribe')
    `)
    
    console.log('📊 现有表:', tables.map((row: any) => row.TABLE_NAME))
    
    // 检查 nqhy_message_tasks 表
    const messageTasksExists = tables.some((row: any) => row.TABLE_NAME === 'nqhy_message_tasks')
    
    if (!messageTasksExists) {
      console.log('⚠️  nqhy_message_tasks 表不存在')
      console.log('💡 请执行以下SQL创建表:')
      console.log('   source sql/init_scheduled_tasks.sql')
    } else {
      console.log('✅ nqhy_message_tasks 表存在')
      
      // 检查表结构
      const columns = await db.execute(`
        SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
        FROM information_schema.COLUMNS 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'nqhy_message_tasks'
        ORDER BY ORDINAL_POSITION
      `)
      
      console.log('📝 表结构:')
      columns.forEach((col: any) => {
        console.log(`   - ${col.COLUMN_NAME}: ${col.DATA_TYPE} ${col.IS_NULLABLE === 'NO' ? 'NOT NULL' : 'NULL'} ${col.COLUMN_DEFAULT ? `DEFAULT ${col.COLUMN_DEFAULT}` : ''}`)
      })
      
      // 检查数据
      const count = await db.execute('SELECT COUNT(*) as count FROM nqhy_message_tasks')
      console.log(`📊 表中数据条数: ${count[0].count}`)
      
      if (count[0].count > 0) {
        const sample = await db.execute('SELECT id, tpl_id, enabled FROM nqhy_message_tasks LIMIT 3')
        console.log('📄 示例数据:')
        sample.forEach((row: any) => {
          console.log(`   - ID: ${row.id}, 模板ID: ${row.tpl_id}, 启用: ${row.enabled}`)
        })
      }
    }
    
    // 检查 user_subscribe 表
    const userSubscribeExists = tables.some((row: any) => row.TABLE_NAME === 'user_subscribe')
    
    if (!userSubscribeExists) {
      console.log('⚠️  user_subscribe 表不存在')
    } else {
      console.log('✅ user_subscribe 表存在')
      
      const userCount = await db.execute('SELECT COUNT(*) as count FROM user_subscribe')
      console.log(`📊 用户订阅数据条数: ${userCount[0].count}`)
    }
    
    console.log('\n🎉 数据库检查完成!')
    
  } catch (error) {
    console.error('❌ 数据库检查失败:', error.message)
    console.error('📋 错误详情:', {
      code: error.code,
      errno: error.errno,
      sqlState: error.sqlState,
      stack: error.stack
    })
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 连接被拒绝，可能的原因:')
      console.log('   1. 数据库服务未启动')
      console.log('   2. SSH隧道未建立')
      console.log('   3. 数据库配置错误')
      console.log('   4. 防火墙阻止连接')
    }
    
    if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.log('\n💡 访问被拒绝，可能的原因:')
      console.log('   1. 用户名或密码错误')
      console.log('   2. 用户没有访问权限')
      console.log('   3. 数据库不存在')
    }
    
    process.exit(1)
  }
}

// 运行检查
checkDatabase()
