/**
 * 调试时区问题的脚本
 */
import { getDb } from '@/db'
import { getChinaTime } from '@/utils/time'

async function debugTimezone() {
  try {
    const db = await getDb()
    if (!db) {
      throw new Error('数据库连接失败')
    }

    console.log('🔍 调试时区问题...\n')

    // 1. 检查数据库时区设置
    console.log('📋 数据库时区设置:')
    const timezoneInfo = await db.execute(`
      SELECT 
        @@global.time_zone as global_tz,
        @@session.time_zone as session_tz,
        @@system_time_zone as system_tz,
        NOW() as db_now,
        UTC_TIMESTAMP() as db_utc
    `)
    
    console.log('全局时区:', timezoneInfo[0]?.global_tz)
    console.log('会话时区:', timezoneInfo[0]?.session_tz)
    console.log('系统时区:', timezoneInfo[0]?.system_tz)
    console.log('数据库 NOW():', timezoneInfo[0]?.db_now)
    console.log('数据库 UTC_TIMESTAMP():', timezoneInfo[0]?.db_utc)

    // 2. 应用时区
    console.log('\n🕐 应用时区:')
    const appTime = getChinaTime()
    console.log('应用当前时间:', appTime)
    console.log('应用时区偏移:', appTime.getTimezoneOffset(), '分钟')

    // 3. 测试插入和查询
    console.log('\n🧪 测试时间插入和查询:')
    
    // 创建测试表（如果不存在）
    await db.execute(`
      CREATE TABLE IF NOT EXISTS timezone_test (
        id INT AUTO_INCREMENT PRIMARY KEY,
        timestamp_col TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        datetime_col DATETIME DEFAULT CURRENT_TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        test_name VARCHAR(100)
      )
    `)

    // 插入测试数据
    const testTime = new Date()
    console.log('插入的 JavaScript 时间:', testTime)
    
    await db.execute(`
      INSERT INTO timezone_test (test_name, timestamp_col, datetime_col) 
      VALUES (?, ?, ?)
    `, ['test-' + Date.now(), testTime, testTime])

    // 查询刚插入的数据
    const result = await db.execute(`
      SELECT 
        test_name,
        timestamp_col,
        datetime_col,
        UNIX_TIMESTAMP(timestamp_col) as timestamp_unix,
        UNIX_TIMESTAMP(datetime_col) as datetime_unix
      FROM timezone_test 
      WHERE test_name LIKE 'test-%'
      ORDER BY id DESC 
      LIMIT 1
    `)

    if (result[0]) {
      console.log('查询结果:')
      console.log('  TIMESTAMP 列:', result[0].timestamp_col)
      console.log('  DATETIME 列:', result[0].datetime_col)
      console.log('  TIMESTAMP Unix:', result[0].timestamp_unix)
      console.log('  DATETIME Unix:', result[0].datetime_unix)
      
      // 转换为 JavaScript Date 对象
      const tsDate = new Date(result[0].timestamp_col)
      const dtDate = new Date(result[0].datetime_col)
      
      console.log('  转换为 JS Date (TIMESTAMP):', tsDate)
      console.log('  转换为 JS Date (DATETIME):', dtDate)
    }

    // 4. 检查现有表的时间字段
    console.log('\n📊 检查现有表的时间字段:')
    
    // 检查 userSubscribe 表
    try {
      const subscribeData = await db.execute(`
        SELECT 
          id,
          createdAt,
          updatedAt,
          lastReceivedAt,
          UNIX_TIMESTAMP(createdAt) as created_unix,
          UNIX_TIMESTAMP(updatedAt) as updated_unix,
          UNIX_TIMESTAMP(lastReceivedAt) as received_unix
        FROM nqhy_subscribe 
        ORDER BY updatedAt DESC 
        LIMIT 3
      `)
      
      console.log('nqhy_subscribe 表样本数据:')
      subscribeData.forEach((row: any, index: number) => {
        console.log(`  记录 ${index + 1}:`)
        console.log(`    ID: ${row.id}`)
        console.log(`    createdAt: ${row.createdAt} (Unix: ${row.created_unix})`)
        console.log(`    updatedAt: ${row.updatedAt} (Unix: ${row.updated_unix})`)
        console.log(`    lastReceivedAt: ${row.lastReceivedAt} (Unix: ${row.received_unix})`)
        
        if (row.createdAt) {
          const jsDate = new Date(row.createdAt)
          console.log(`    转换为 JS Date: ${jsDate}`)
        }
        console.log('')
      })
    } catch (error) {
      console.log('无法查询 nqhy_subscribe 表:', error.message)
    }

    // 5. 时区转换测试
    console.log('\n🔄 时区转换测试:')
    const conversionTest = await db.execute(`
      SELECT 
        NOW() as local_now,
        UTC_TIMESTAMP() as utc_now,
        CONVERT_TZ(NOW(), @@session.time_zone, '+00:00') as to_utc,
        CONVERT_TZ(NOW(), @@session.time_zone, '+08:00') as to_china,
        UNIX_TIMESTAMP(NOW()) as now_unix
    `)
    
    if (conversionTest[0]) {
      console.log('本地时间:', conversionTest[0].local_now)
      console.log('UTC 时间:', conversionTest[0].utc_now)
      console.log('转换为 UTC:', conversionTest[0].to_utc)
      console.log('转换为中国时间:', conversionTest[0].to_china)
      console.log('Unix 时间戳:', conversionTest[0].now_unix)
    }

    // 清理测试数据
    await db.execute(`DELETE FROM timezone_test WHERE test_name LIKE 'test-%'`)
    
    console.log('\n✅ 时区调试完成')

  } catch (error) {
    console.error('❌ 时区调试失败:', error.message)
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  debugTimezone()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error('脚本执行失败:', error)
      process.exit(1)
    })
}

export { debugTimezone }
