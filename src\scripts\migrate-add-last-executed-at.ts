#!/usr/bin/env tsx

/**
 * 数据库迁移脚本：添加 lastExecutedAt 字段
 * 
 * 使用方法：
 * npx tsx src/scripts/migrate-add-last-executed-at.ts
 */

import { getDb } from '../db'
import { logger } from '../lib/logger'

async function addLastExecutedAtField() {
  try {
    logger.info('开始执行数据库迁移：添加 lastExecutedAt 字段')
    
    const db = await getDb()
    if (!db) {
      throw new Error('数据库连接失败')
    }

    // 检查字段是否已存在
    const checkFieldQuery = `
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'nqhy_message_tasks' 
        AND COLUMN_NAME = 'last_executed_at'
    `
    
    const [existingField] = await db.execute(checkFieldQuery)
    
    if (Array.isArray(existingField) && existingField.length > 0) {
      logger.info('字段 last_executed_at 已存在，跳过迁移')
      return
    }

    // 添加新字段
    const addFieldQuery = `
      ALTER TABLE \`nqhy_message_tasks\` 
      ADD COLUMN \`last_executed_at\` TIMESTAMP NULL 
      COMMENT '上次执行时间，专门用于判断任务执行间隔'
    `
    
    await db.execute(addFieldQuery)
    logger.info('成功添加 last_executed_at 字段')

    // 可选：将现有的 updatedAt 值复制到 lastExecutedAt
    const copyDataQuery = `
      UPDATE \`nqhy_message_tasks\` 
      SET \`last_executed_at\` = \`updated_at\` 
      WHERE \`last_executed_at\` IS NULL
    `
    
    const [updateResult] = await db.execute(copyDataQuery)
    logger.info('已将现有 updatedAt 值复制到 lastExecutedAt', { 
      affectedRows: (updateResult as any).affectedRows 
    })

    logger.info('数据库迁移完成')
    
  } catch (error) {
    logger.error('数据库迁移失败', { 
      error: error.message,
      stack: error.stack 
    })
    throw error
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  addLastExecutedAtField()
    .then(() => {
      logger.info('迁移脚本执行完成')
      process.exit(0)
    })
    .catch((error) => {
      logger.error('迁移脚本执行失败', { error: error.message })
      process.exit(1)
    })
}

export { addLastExecutedAtField }
