/**
 * 测试 Kafka 和 Redis 连接的脚本
 */
import { getRedis, closeRedis } from '@/redis'
import { kafka } from 'kafkajs'
import { kafkaConfig } from '@/config/kafka'
import { logger } from '@/lib/logger'

async function testRedisConnection() {
  console.log('🔄 测试 Redis 连接...')
  
  try {
    const redis = await getRedis()
    
    // 测试基本操作
    await redis.set('test:connection', 'success', { EX: 60 })
    const result = await redis.get('test:connection')
    
    if (result === 'success') {
      console.log('✅ Redis 连接测试成功')
      
      // 测试一些基本操作
      await redis.set('test:counter', '0')
      await redis.incr('test:counter')
      const counter = await redis.get('test:counter')
      console.log(`📊 Redis 计数器测试: ${counter}`)
      
      // 清理测试数据
      await redis.del('test:connection', 'test:counter')
      
      return true
    } else {
      console.log('❌ Redis 连接测试失败: 数据不匹配')
      return false
    }
  } catch (error) {
    console.error('❌ Redis 连接测试失败:', error.message)
    return false
  }
}

async function testKafkaConnection() {
  console.log('🔄 测试 Kafka 连接...')
  
  try {
    const kafkaClient = kafka(kafkaConfig)
    const admin = kafkaClient.admin()
    
    // 连接到 Kafka
    await admin.connect()
    console.log('✅ Kafka 管理员连接成功')
    
    // 获取集群信息
    const metadata = await admin.fetchTopicMetadata()
    console.log(`📊 Kafka 集群信息: ${metadata.topics.length} 个主题`)
    
    // 创建测试主题（如果不存在）
    const testTopic = 'test-connection'
    const existingTopics = metadata.topics.map(t => t.name)
    
    if (!existingTopics.includes(testTopic)) {
      await admin.createTopics({
        topics: [
          {
            topic: testTopic,
            numPartitions: 1,
            replicationFactor: 1,
          },
        ],
      })
      console.log(`✅ 创建测试主题: ${testTopic}`)
    }
    
    await admin.disconnect()
    
    // 测试生产者
    const producer = kafkaClient.producer()
    await producer.connect()
    console.log('✅ Kafka 生产者连接成功')
    
    const testMessage = {
      timestamp: new Date().toISOString(),
      message: 'Kafka connection test',
      source: 'test-script'
    }
    
    await producer.send({
      topic: testTopic,
      messages: [
        {
          key: 'test-key',
          value: JSON.stringify(testMessage),
        },
      ],
    })
    console.log('✅ Kafka 消息发送成功')
    
    await producer.disconnect()
    
    // 测试消费者
    const consumer = kafkaClient.consumer({ groupId: 'test-group' })
    await consumer.connect()
    console.log('✅ Kafka 消费者连接成功')
    
    await consumer.subscribe({ topic: testTopic, fromBeginning: true })
    
    let messageReceived = false
    const timeout = setTimeout(() => {
      if (!messageReceived) {
        console.log('⚠️  Kafka 消息消费超时')
      }
    }, 10000)
    
    await consumer.run({
      eachMessage: async ({ topic, partition, message }) => {
        const messageValue = message.value?.toString()
        if (messageValue) {
          const parsedMessage = JSON.parse(messageValue)
          console.log('✅ Kafka 消息消费成功:', parsedMessage.message)
          messageReceived = true
          clearTimeout(timeout)
          await consumer.stop()
        }
      },
    })
    
    await consumer.disconnect()
    
    return true
  } catch (error) {
    console.error('❌ Kafka 连接测试失败:', error.message)
    return false
  }
}

async function main() {
  console.log('🚀 开始测试 Kafka 和 Redis 连接...\n')
  
  const results = {
    redis: false,
    kafka: false,
  }
  
  // 测试 Redis
  results.redis = await testRedisConnection()
  console.log('')
  
  // 测试 Kafka
  results.kafka = await testKafkaConnection()
  console.log('')
  
  // 输出结果
  console.log('📋 测试结果汇总:')
  console.log(`Redis: ${results.redis ? '✅ 成功' : '❌ 失败'}`)
  console.log(`Kafka: ${results.kafka ? '✅ 成功' : '❌ 失败'}`)
  
  if (results.redis && results.kafka) {
    console.log('\n🎉 所有连接测试通过！')
  } else {
    console.log('\n⚠️  部分连接测试失败，请检查配置和服务状态')
  }
  
  // 清理连接
  await closeRedis()
  
  process.exit(results.redis && results.kafka ? 0 : 1)
}

// 运行测试
if (require.main === module) {
  main().catch((error) => {
    console.error('❌ 测试脚本执行失败:', error)
    process.exit(1)
  })
}

export { testRedisConnection, testKafkaConnection }
