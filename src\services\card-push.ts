import { getAccessToken } from '@/redis'
import { logger } from '@/lib/logger'

interface CardPushData {
  reward_index: string
  reward_count: string // 格式: "[10,20]" 或 "[777]"
}

interface CardPushRequest {
  aid: number // 1=抖音, 2=抖音极速版
  mp_id: string // 小程序ID
  open_id: string[] // 用户openId数组，最多100个
  card_id: number // 卡片ID
  data: CardPushData
}

interface CardPushResult {
  [openId: string]: number // 0=成功, 其他值为错误码
}

interface CardPushResponse {
  BaseResp: {
    StatusCode: number
    StatusMessage: string
  }
  data: {
    push_result: CardPushResult
  }
  err_msg: string
  err_no: number
  log_id: string
}

/**
 * 推送异形卡给指定用户
 * @param accessToken 访问令牌
 * @param request 推送请求参数
 * @returns 推送结果
 */
export async function pushFeedCard(
  accessToken: string,
  request: CardPushRequest
): Promise<CardPushResponse> {
  const url = 'https://minigame.zijieapi.com/mgplatform/api/apps/open/push_feed_card'
  
  try {
    logger.info('开始推送异形卡', {
      cardId: request.card_id,
      userCount: request.open_id.length,
      aid: request.aid
    })

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'access-token': accessToken
      },
      body: JSON.stringify(request)
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const result = await response.json() as CardPushResponse
    
    // 记录推送结果统计
    const pushResult = result.data?.push_result || {}
    const successCount = Object.values(pushResult).filter(code => code === 0).length
    const failCount = Object.values(pushResult).filter(code => code !== 0).length
    
    logger.info('异形卡推送完成', {
      cardId: request.card_id,
      totalUsers: request.open_id.length,
      successCount,
      failCount,
      errNo: result.err_no
    })

    // 记录失败的用户和错误码
    Object.entries(pushResult).forEach(([openId, code]) => {
      if (code !== 0) {
        logger.warn('用户推送失败', {
          openId,
          errorCode: code,
          errorMsg: getErrorMessage(code)
        })
      }
    })

    return result
  } catch (error) {
    logger.error('推送异形卡失败', {
      cardId: request.card_id,
      error: error instanceof Error ? error.message : String(error)
    })
    throw error
  }
}

/**
 * 批量推送异形卡（支持大量用户，自动分批）
 * @param appId 应用ID
 * @param request 推送请求参数
 * @returns 推送结果汇总
 */
export async function batchPushFeedCard(
  appId: string,
  request: Omit<CardPushRequest, 'open_id'> & { open_id: string[] }
): Promise<{
  totalUsers: number
  successCount: number
  failCount: number
  results: CardPushResult
}> {
  const accessToken = await getAccessToken(appId)
  if (!accessToken) {
    throw new Error(`未找到应用 ${appId} 的 access_token`)
  }

  const { open_id: allOpenIds, ...baseRequest } = request
  const BATCH_SIZE = 100 // API限制每次最多100个用户
  const allResults: CardPushResult = {}
  
  // 分批处理
  for (let i = 0; i < allOpenIds.length; i += BATCH_SIZE) {
    const batchOpenIds = allOpenIds.slice(i, i + BATCH_SIZE)
    
    try {
      const batchRequest: CardPushRequest = {
        ...baseRequest,
        open_id: batchOpenIds
      }
      
      const batchResponse = await pushFeedCard(accessToken, batchRequest)
      
      // 合并结果
      if (batchResponse.data?.push_result) {
        Object.assign(allResults, batchResponse.data.push_result)
      }
      
      // 批次间延迟，避免QPS超限
      if (i + BATCH_SIZE < allOpenIds.length) {
        await new Promise(resolve => setTimeout(resolve, 100))
      }
    } catch (error) {
      logger.error('批次推送失败', {
        batchStart: i,
        batchSize: batchOpenIds.length,
        error: error instanceof Error ? error.message : String(error)
      })
      
      // 标记这批用户为失败
      batchOpenIds.forEach(openId => {
        allResults[openId] = 5 // 服务器内部错误
      })
    }
  }
  
  const successCount = Object.values(allResults).filter(code => code === 0).length
  const failCount = Object.values(allResults).filter(code => code !== 0).length
  
  return {
    totalUsers: allOpenIds.length,
    successCount,
    failCount,
    results: allResults
  }
}

/**
 * 根据错误码获取错误信息
 */
function getErrorMessage(code: number): string {
  const errorMessages: Record<number, string> = {
    0: '成功',
    1: '无效的用户openID',
    2: 'openID关联的宿主端ID和输入的宿主端ID不匹配',
    3: '卡片ID不存在或卡片未生效',
    4: '卡片超过当日总下发次数限制',
    5: '服务器内部错误，可稍后重试',
    6: '该用户未订阅小游戏推送或订阅已失效',
    7: '参数错误，重点关注reward_index、reward_count参数',
    8: '当日已对该用户推送过该游戏卡，无需重复推送',
    9: '当前用户命中平台负反馈过滤，当日无需重试'
  }
  
  return errorMessages[code] || `未知错误码: ${code}`
}

/**
 * 创建卡片推送数据
 * @param rewardIndex 奖励索引 (1,2,3,4...)
 * @param rewardCounts 奖励数量数组
 * @returns 卡片数据
 */
export function createCardData(rewardIndex: number, rewardCounts: number[]): CardPushData {
  return {
    reward_index: String(rewardIndex),
    reward_count: JSON.stringify(rewardCounts)
  }
}