import { ScheduledMessageTask } from "@/config"
import { getDb } from "@/db"
import { log } from "@/lib/logger"
import { scheduledTasks, xthyUserData } from "@/schema"
import { BaseTaskResult } from "@/types/task-message"
import { formatChinaTime, getChinaTime } from "@/utils/time"
import { and, eq, gte, lte } from "drizzle-orm"
import { batchPushFeedCard, createCardData } from "../card-push"

/**
 * 批量发送卡片给符合条件的用户
 * @param task 任务配置
 */
export async function sendBatchCards(task:ScheduledMessageTask): Promise<BaseTaskResult> {
    const {
      tplId,
      messageData,
      page,
      max
    } = task
    const db = await getDb()
    if (!db) {
      throw new Error('数据库未初始化')
    }
  
    // 查询昨天到5天前注册的用户（签到用户）
    const now = getChinaTime()

    // 计算昨天和5天前的日期
    const yesterday = new Date(now)
    yesterday.setDate(yesterday.getDate() - 1)
    yesterday.setHours(0, 0, 0, 0)

    const fiveDaysAgo = new Date(now)
    fiveDaysAgo.setDate(fiveDaysAgo.getDate() - 5)
    fiveDaysAgo.setHours(0, 0, 0, 0)

    const endOfYesterday = new Date(yesterday)
    endOfYesterday.setHours(23, 59, 59, 999)

    log.task.start(tplId, '开始查询签到用户', {
      fiveDaysAgo: formatChinaTime(fiveDaysAgo),
      endOfYesterday: formatChinaTime(endOfYesterday)
    })

    // 查询昨天到5天前注册的用户
    const allSubscribers = await db
      .select({
        openId: xthyUserData.openId,
        createTime: xthyUserData.createTime,
        nonId: xthyUserData.nonId
      })
      .from(xthyUserData)
      .where(and(
        gte(xthyUserData.createTime, fiveDaysAgo),
        lte(xthyUserData.createTime, endOfYesterday)
      ))
      .execute()

    // 为每个用户计算签到天数索引和奖励数据
    // 按注册日期将用户分成5批，每批用户的奖励索引相同
    const userBatches: Array<{
      dayIndex: number
      users: Array<{ openId: string; nonId: number; createTime: Date }>
    }> = []

    // 为每天创建一个批次
    for (let dayOffset = 1; dayOffset <= 5; dayOffset++) {
      const targetDate = new Date(now)
      targetDate.setDate(targetDate.getDate() - dayOffset)
      targetDate.setHours(0, 0, 0, 0)

      const nextDay = new Date(targetDate)
      nextDay.setDate(nextDay.getDate() + 1)
      nextDay.setHours(0, 0, 0, 0)

      // 筛选出这一天注册的用户
      const dayUsers = allSubscribers.filter(user => {
        const userCreateTime = new Date(user.createTime)
        return userCreateTime >= targetDate && userCreateTime < nextDay
      })

      if (dayUsers.length > 0) {
        userBatches.push({
          dayIndex: dayOffset,
          users: dayUsers,
        })
      }
    }

    log.task.progress(tplId, '用户分批完成', {
      totalBatches: userBatches.length,
      batchDetails: userBatches.map(batch => ({
        dayIndex: batch.dayIndex,
        userCount: batch.users.length
      }))
    })
  
    const totalUsers = userBatches.reduce((sum, batch) => sum + batch.users.length, 0)

    log.task.progress(tplId, '用户查询和分批完成', {
      totalUsers: allSubscribers.length,
      validBatches: userBatches.length,
      processableUsers: totalUsers
    })

    if (userBatches.length === 0) {
      return {
        totalUsers: 0,
        results: [],
        message: '没有符合条件的用户'
      }
    }
  
    const results = []

    // 按批次发送卡片，每批用户使用相同的奖励数据
    for (const batch of userBatches) {
      try {
        // 检查批次是否有有效用户
        if (!batch.users || batch.users.length === 0) {
          log.task.skip(tplId, `第${batch.dayIndex}天签到批次无有效用户，跳过`, {
            dayIndex: batch.dayIndex
          })
          continue
        }

        log.task.progress(tplId, `开始处理第${batch.dayIndex}天签到用户批次`, {
          dayIndex: batch.dayIndex,
          userCount: batch.users.length
        })

        // 使用批次的奖励数据创建卡片
        const cardData = createCardData(batch.dayIndex, messageData as number[])

        // 提取所有用户的openId，过滤掉空值
        const openIds = batch.users
          .map(user => user.openId)
          .filter((openId): openId is string => openId !== null && openId !== undefined)

        // 解析卡片ID，添加错误检查
        const cardId = parseInt(task.tplId)
        if (isNaN(cardId)) {
          throw new Error(`无效的卡片ID: ${task.tplId}`)
        }

        // 调用批量卡片推送API
        const cardResult = await batchPushFeedCard(task.appId, {
          aid: 1, // 抖音
          mp_id: task.appId,
          card_id: cardId,
          data: cardData,
          open_id: openIds
        })

        log.task.success(tplId, `第${batch.dayIndex}天签到批次发送完成`, {
          dayIndex: batch.dayIndex,
          totalUsers: batch.users.length,
          successCount: cardResult.successCount,
          failCount: cardResult.failCount
        })

        // 为每个用户记录结果
        batch.users.forEach(user => {
          if (!user.openId) {
            results.push({
              openId: user.openId || 'unknown',
              success: false,
              error: 'openId为空',
              dayIndex: batch.dayIndex
            })
            return
          }

          // 检查 cardResult 结构
          let isSuccess = false
          let errorMsg = null

          if (cardResult.results && typeof cardResult.results === 'object') {
            const userResult = cardResult.results[user.openId]
            isSuccess = userResult === 0
            errorMsg = isSuccess ? null : `错误码: ${userResult}`
          } else {
            // 如果没有详细结果，根据总体成功数判断
            isSuccess = cardResult.successCount > 0
            errorMsg = isSuccess ? null : '发送失败'
          }

          results.push({
            openId: user.openId,
            success: isSuccess,
            error: errorMsg,
            dayIndex: batch.dayIndex
          })
        })

      } catch (error) {
        log.task.error(tplId, `第${batch.dayIndex}天签到批次发送失败`, {
          dayIndex: batch.dayIndex,
          userCount: batch.users.length,
          error: error instanceof Error ? error.message : '未知错误'
        })

        // 为这批所有用户标记为失败
        batch.users.forEach(user => {
          results.push({
            openId: user.openId,
            success: false,
            error: error instanceof Error ? error.message : '未知错误',
            dayIndex: batch.dayIndex
          })
        })
      }
    }
  
    log.task.complete(tplId, '签到卡片发送任务完成', {
      totalBatches: userBatches.length,
      totalUsers,
      successCount: results.filter(r => r.success).length,
      failCount: results.filter(r => !r.success).length
    })

    return {
      totalUsers,
      results,
      message: `签到卡片发送完成，共处理 ${userBatches.length} 个批次，${totalUsers} 个用户`
    }
  }