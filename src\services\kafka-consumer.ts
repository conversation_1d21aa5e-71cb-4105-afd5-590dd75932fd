import { topics } from '@/config/kafka'
import { logger } from '@/lib/logger'
import { closeKafka, subscribeToTopic } from './kafka'
import { messageHandlers } from './kafka-handlers'

// 创建专用日志器
const consumerLogger = logger.child({ component: 'kafka-consumer' })

/**
 * 启动Kafka消费者
 */
export async function startKafkaConsumer(): Promise<void> {
  try {
    consumerLogger.info('启动Kafka消费者服务')

    // 订阅所有主题
    const subscriptionPromises = Object.entries(messageHandlers).map(
      async ([topic, handler]) => {
        try {
          await subscribeToTopic(topic as any, handler)
          consumerLogger.info('成功订阅主题', { topic })
        }
        catch (error) {
          consumerLogger.error('订阅主题失败', {
            topic,
            error: error.message,
          })
          throw error
        }
      },
    )

    await Promise.all(subscriptionPromises)

    consumerLogger.info('Kafka消费者服务启动完成', {
      subscribedTopics: Object.keys(messageHandlers),
    })
  }
  catch (error) {
    consumerLogger.error('启动Kafka消费者服务失败', {
      error: error.message,
    })
    throw error
  }
}

/**
 * 停止Kafka消费者
 */
export async function stopKafkaConsumer(): Promise<void> {
  try {
    consumerLogger.info('停止Kafka消费者服务')
    await closeKafka()
    consumerLogger.info('Kafka消费者服务已停止')
  }
  catch (error) {
    consumerLogger.error('停止Kafka消费者服务失败', {
      error: error.message,
    })
    throw error
  }
}

// 处理进程退出时的清理
process.on('SIGTERM', async () => {
  consumerLogger.info('收到SIGTERM信号，正在关闭Kafka消费者')
  await stopKafkaConsumer()
})

process.on('SIGINT', async () => {
  consumerLogger.info('收到SIGINT信号，正在关闭Kafka消费者')
  await stopKafkaConsumer()
})
