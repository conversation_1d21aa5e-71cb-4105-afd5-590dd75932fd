import type { EachMessagePayload } from 'kafkajs'
import type { TaskExecutionContext, TaskMessage } from '@/types/task-message'
import { topics } from '@/config/kafka'
import { logger } from '@/lib/logger'
import { taskHandlers } from './task-handlers'

// 创建专用日志器
const handlerLogger = logger.child({ component: 'kafka-handler' })

/**
 * 抖音消息处理器
 */
export async function handleDouyinMessage(payload: EachMessagePayload): Promise<void> {
  const { message } = payload

  try {
    const messageValue = message.value?.toString()
    if (!messageValue) {
      handlerLogger.warn('收到空的抖音消息')
      return
    }

    const data = JSON.parse(messageValue)

    handlerLogger.info('处理抖音消息', {
      messageKey: message.key?.toString(),
      messageData: data,
    })

    // 这里添加具体的抖音消息处理逻辑
    // 例如：发送订阅消息、更新用户状态等

    // 示例处理逻辑
    if (data.type === 'subscribe') {
      await processDouyinSubscribe(data)
    }
    else if (data.type === 'token_refresh') {
      await processTokenRefresh(data)
    }
  }
  catch (error) {
    handlerLogger.error('抖音消息处理失败', {
      error: error.message,
      messageValue: message.value?.toString(),
    })
    throw error
  }
}

/**
 * 抖音Token处理器
 */
export async function handleDouyinToken(payload: EachMessagePayload): Promise<void> {
  const { message } = payload

  try {
    const messageValue = message.value?.toString()
    if (!messageValue) {
      handlerLogger.warn('收到空的Token消息')
      return
    }

    const tokenData = JSON.parse(messageValue)

    handlerLogger.info('处理Token消息', {
      appId: tokenData.appId,
      tokenLength: tokenData.access_token?.length,
    })

    // 这里添加Token处理逻辑
    // 例如：更新Redis中的Token、通知相关服务等
    await processTokenUpdate(tokenData)
  }
  catch (error) {
    handlerLogger.error('Token消息处理失败', {
      error: error.message,
      messageValue: message.value?.toString(),
    })
    throw error
  }
}

/**
 * 系统日志处理器
 */
export async function handleSystemLog(payload: EachMessagePayload): Promise<void> {
  const { message } = payload

  try {
    const messageValue = message.value?.toString()
    if (!messageValue) {
      return
    }

    const logData = JSON.parse(messageValue)

    handlerLogger.info('处理系统日志', {
      logLevel: logData.level,
      component: logData.component,
    })

    // 这里可以添加日志聚合、分析等逻辑
    await processSystemLog(logData)
  }
  catch (error) {
    handlerLogger.error('系统日志处理失败', {
      error: error.message,
    })
    // 日志处理失败不应该抛出异常，避免影响其他消息处理
  }
}

/**
 * 用户行为处理器
 */
export async function handleUserAction(payload: EachMessagePayload): Promise<void> {
  const { message } = payload

  try {
    const messageValue = message.value?.toString()
    if (!messageValue) {
      return
    }

    const actionData = JSON.parse(messageValue)

    handlerLogger.info('处理用户行为', {
      userId: actionData.userId,
      action: actionData.action,
    })

    // 这里添加用户行为分析逻辑
    await processUserAction(actionData)
  }
  catch (error) {
    handlerLogger.error('用户行为处理失败', {
      error: error.message,
    })
    throw error
  }
}

// 具体的业务处理函数

async function processDouyinSubscribe(data: any): Promise<void> {
  handlerLogger.info('处理抖音订阅', { data })
  // 实现订阅逻辑
}

async function processTokenRefresh(data: any): Promise<void> {
  handlerLogger.info('处理Token刷新', { appId: data.appId })
  // 实现Token刷新逻辑
}

async function processTokenUpdate(tokenData: any): Promise<void> {
  handlerLogger.info('更新Token', { appId: tokenData.appId })
  // 实现Token更新逻辑，例如更新Redis
}

async function processSystemLog(logData: any): Promise<void> {
  handlerLogger.debug('处理系统日志', { level: logData.level })
  // 实现日志处理逻辑
}

async function processUserAction(actionData: any): Promise<void> {
  handlerLogger.info('处理用户行为', {
    userId: actionData.userId,
    action: actionData.action,
  })
  // 实现用户行为分析逻辑
}

/**
 * 定时任务处理器
 */
export async function handleScheduledTasks(payload: EachMessagePayload): Promise<void> {
  const { message } = payload

  try {
    const messageValue = message.value?.toString()
    if (!messageValue) {
      handlerLogger.warn('收到空的定时任务消息')
      return
    }

    const taskMessage: TaskMessage = JSON.parse(messageValue)

    handlerLogger.info('收到定时任务消息', {
      taskId: taskMessage.taskId,
      taskType: taskMessage.taskType,
      scheduledTime: taskMessage.scheduledTime,
      retryCount: taskMessage.retryCount || 0,
    })

    // 获取对应的任务处理器
    const handler = taskHandlers[taskMessage.taskType]
    if (!handler) {
      handlerLogger.error('未找到任务处理器', {
        taskType: taskMessage.taskType,
        taskId: taskMessage.taskId,
      })
      return
    }

    // 验证任务参数
    if (handler.validateParams && !handler.validateParams(taskMessage.parameters)) {
      handlerLogger.error('任务参数验证失败', {
        taskId: taskMessage.taskId,
        taskType: taskMessage.taskType,
        parameters: taskMessage.parameters,
      })
      return
    }

    // 创建执行上下文
    const context: TaskExecutionContext = {
      taskMessage,
      startTime: new Date(),
      logger: handlerLogger.child({ taskId: taskMessage.taskId }),
      retryCount: taskMessage.retryCount || 0,
      isRetry: (taskMessage.retryCount || 0) > 0,
    }

    // 执行任务
    const result = await handler.handle(context)

    handlerLogger.info('定时任务执行完成', {
      taskId: result.taskId,
      taskType: result.taskType,
      success: result.success,
      duration: result.duration,
      retryCount: result.retryCount,
    })

    // 如果任务失败且还有重试次数，可以考虑重新发送到队列
    if (!result.success && result.retryCount < (taskMessage.maxRetries || 3)) {
      await handleTaskRetry(taskMessage, result.error || '未知错误')
    }
  }
  catch (error) {
    handlerLogger.error('定时任务处理失败', {
      error: error.message,
      messageValue: message.value?.toString(),
    })
    throw error
  }
}

/**
 * 处理任务重试
 */
async function handleTaskRetry(taskMessage: TaskMessage, error: string): Promise<void> {
  const handler = taskHandlers[taskMessage.taskType]
  const retryDelay = handler.getRetryDelay ? handler.getRetryDelay(taskMessage.retryCount || 0) : 60000

  handlerLogger.warn('任务执行失败，准备重试', {
    taskId: taskMessage.taskId,
    taskType: taskMessage.taskType,
    retryCount: taskMessage.retryCount || 0,
    retryDelay,
    error,
  })

  // 这里可以实现重试逻辑，比如延迟后重新发送到队列
  // 或者发送到专门的重试队列
  setTimeout(async () => {
    try {
      const { sendMessage } = await import('./kafka')

      const retryMessage: TaskMessage = {
        ...taskMessage,
        retryCount: (taskMessage.retryCount || 0) + 1,
        createdAt: new Date().toISOString(),
      }

      await sendMessage(topics.SCHEDULED_TASKS, retryMessage, taskMessage.taskId)

      handlerLogger.info('重试任务已重新发送到队列', {
        taskId: taskMessage.taskId,
        retryCount: retryMessage.retryCount,
      })
    }
    catch (retryError) {
      handlerLogger.error('重试任务发送失败', {
        taskId: taskMessage.taskId,
        error: retryError.message,
      })
    }
  }, retryDelay)
}

// 导出消息处理器映射
export const messageHandlers = {
  [topics.DOUYIN_MESSAGE]: handleDouyinMessage,
  [topics.DOUYIN_TOKEN]: handleDouyinToken,
  [topics.SYSTEM_LOG]: handleSystemLog,
  [topics.USER_ACTION]: handleUserAction,
  [topics.SCHEDULED_TASKS]: handleScheduledTasks,
}
