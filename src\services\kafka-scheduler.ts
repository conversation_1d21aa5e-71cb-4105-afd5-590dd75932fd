import { getMessageTasks } from '@/config'
import { shouldExecuteTask } from '@/common'
import { distributedLock } from './distributed-lock'
import { sendMessage, topics } from './kafka'
import { schedulerLogger } from '@/lib/logger'
import { TaskType, DouyinMessageTask, TaskScheduleConfig } from '@/types/task-message'

/**
 * 基于Kafka的定时任务调度器
 * 只负责按时发送任务消息，不执行具体任务
 */
export class KafkaScheduler {
  private isRunning = false
  private intervalId: NodeJS.Timeout | null = null
  private scheduleConfigs: TaskScheduleConfig[] = []

  constructor() {
    // 构造函数中不再初始化，改为在start方法中异步初始化
  }

  /**
   * 初始化调度配置（异步）
   */
  private async initializeScheduleConfigs() {
    try {
      // 从数据库加载最新的任务配置
      const messageTasks = await getMessageTasks()

      // 将现有的messageTasks转换为调度配置
      this.scheduleConfigs = messageTasks.map(task => ({
        taskType: TaskType.DOUYIN_MESSAGE,
        cronExpression: task.cronExpression,
        enabled: task.enabled,
        maxRetries: 3,
        priority: 1,
        parameters: {
          tplId: task.tplId,
          messageData: task.messageData,
          enabled: task.enabled,
          cronExpression: task.cronExpression,
        },
      }))

      schedulerLogger.info('调度配置初始化完成', {
        configCount: this.scheduleConfigs.length,
        enabledCount: this.scheduleConfigs.filter(c => c.enabled).length,
      })
    } catch (error) {
      schedulerLogger.error('Kafka调度配置初始化失败', { error: error.message })
      this.scheduleConfigs = []
    }
  }

  /**
   * 启动调度器
   */
  async start() {
    if (this.isRunning) {
      schedulerLogger.warn('调度器已在运行中')
      return
    }

    // 先初始化配置
    await this.initializeScheduleConfigs()

    // 检查是否启用
    if (process.env.SCHEDULER_ENABLED === 'false') {
      schedulerLogger.info('定时任务调度器已禁用')
      return
    }

    // 检查是否为主实例
    const isMainInstance = process.env.SCHEDULER_MAIN_INSTANCE !== 'false'
    if (!isMainInstance) {
      schedulerLogger.info('当前实例不是主实例，跳过启动定时任务调度器')
      return
    }

    this.isRunning = true
    schedulerLogger.info('启动Kafka定时任务调度器', {
      nodeEnv: process.env.NODE_ENV,
      instanceId: process.env.INSTANCE_ID || 'unknown',
      mode: 'kafka',
    })

    // 立即执行一次检查
    this.checkAndScheduleTasks()

    // 每分钟检查一次
    this.intervalId = setInterval(() => {
      this.checkAndScheduleTasks()
    }, 60 * 1000)

    // 优雅关闭处理
    this.setupGracefulShutdown()
  }

  /**
   * 停止调度器
   */
  stop() {
    if (!this.isRunning) {
      return
    }

    this.isRunning = false
    if (this.intervalId) {
      clearInterval(this.intervalId)
      this.intervalId = null
    }

    schedulerLogger.info('Kafka定时任务调度器已停止')
  }

  /**
   * 检查并调度任务
   */
  private async checkAndScheduleTasks() {
    schedulerLogger.debug('检查定时任务', { timestamp: new Date().toISOString() })

    // 重新加载配置以获取最新的任务
    await this.initializeScheduleConfigs()

    for (const config of this.scheduleConfigs) {
      if (!config.enabled) {
        continue
      }

      if (shouldExecuteTask(config.cronExpression)) {
        await this.scheduleTask(config)
      }
    }
  }

  /**
   * 调度单个任务（发送到Kafka）
   */
  private async scheduleTask(config: TaskScheduleConfig) {
    const lockKey = `kafka_schedule_${config.taskType}_${config.cronExpression}`

    await distributedLock.executeWithLock(
      lockKey,
      async () => {
        await this.sendTaskMessage(config)
      },
      300 // 5分钟锁过期时间
    )
  }

  /**
   * 发送任务消息到Kafka
   */
  private async sendTaskMessage(config: TaskScheduleConfig) {
    try {
      const now = new Date()
      const taskId = this.generateTaskId(config.taskType, now)

      let taskMessage: any

      switch (config.taskType) {
        case TaskType.DOUYIN_MESSAGE:
          taskMessage = this.createDouyinMessageTask(taskId, config, now)
          break
        default:
          schedulerLogger.warn('未知的任务类型', { taskType: config.taskType })
          return
      }

      // 发送消息到Kafka
      await sendMessage(
        topics.SCHEDULED_TASKS,
        taskMessage,
        taskId,
        config.priority || 0
      )

      schedulerLogger.info('任务消息已发送到Kafka', {
        taskId,
        taskType: config.taskType,
        scheduledTime: now.toISOString(),
      })

    } catch (error) {
      schedulerLogger.error('发送任务消息失败', {
        taskType: config.taskType,
        error: error.message,
      })
    }
  }

  /**
   * 创建抖音消息任务
   */
  private createDouyinMessageTask(
    taskId: string,
    config: TaskScheduleConfig,
    scheduledTime: Date
  ): DouyinMessageTask {
    return {
      taskType: TaskType.DOUYIN_MESSAGE,
      taskId,
      scheduledTime: scheduledTime.toISOString(),
      createdAt: new Date().toISOString(),
      retryCount: 0,
      maxRetries: config.maxRetries,
      priority: config.priority,
      parameters: config.parameters,
    }
  }

  /**
   * 生成任务ID
   */
  private generateTaskId(taskType: TaskType, scheduledTime: Date): string {
    const timestamp = scheduledTime.getTime()
    const random = Math.random().toString(36).substring(2, 8)
    return `${taskType}_${timestamp}_${random}`
  }

  /**
   * 手动触发任务
   */
  async triggerTask(taskType: TaskType, parameters: any): Promise<string> {
    const config = this.scheduleConfigs.find(c => c.taskType === taskType)
    if (!config) {
      throw new Error(`未找到任务类型 ${taskType} 的配置`)
    }

    const now = new Date()
    const taskId = this.generateTaskId(taskType, now)

    const customConfig: TaskScheduleConfig = {
      ...config,
      parameters: { ...config.parameters, ...parameters },
    }

    await this.sendTaskMessage(customConfig)

    schedulerLogger.info('手动触发任务', {
      taskId,
      taskType,
      triggeredAt: now.toISOString(),
    })

    return taskId
  }

  /**
   * 添加新的调度配置
   */
  addScheduleConfig(config: TaskScheduleConfig) {
    this.scheduleConfigs.push(config)
    schedulerLogger.info('添加新的调度配置', {
      taskType: config.taskType,
      cronExpression: config.cronExpression,
      enabled: config.enabled,
    })
  }

  /**
   * 获取调度配置
   */
  getScheduleConfigs(): TaskScheduleConfig[] {
    return [...this.scheduleConfigs]
  }

  /**
   * 设置优雅关闭
   */
  private setupGracefulShutdown() {
    const shutdown = () => {
      schedulerLogger.info('收到关闭信号，停止Kafka调度器')
      this.stop()
    }

    process.on('SIGTERM', shutdown)
    process.on('SIGINT', shutdown)
  }
}

// 导出单例实例
export const kafkaScheduler = new KafkaScheduler()
