import { Kafka, Producer, Consumer, EachMessagePayload } from 'kafkajs'
import { kafkaConfig, producerConfig, consumerConfig, topics, type TopicName } from '@/config/kafka'
import { logger } from '@/lib/logger'

// 创建Kafka客户端实例
const kafka = new Kafka(kafkaConfig)

// 创建专用日志器
const kafkaLogger = logger.child({ component: 'kafka' })

// Producer实例
let producer: Producer | null = null

// Consumer实例
let consumer: Consumer | null = null

/**
 * 获取Producer实例
 */
export async function getProducer(): Promise<Producer> {
  if (!producer) {
    producer = kafka.producer(producerConfig)
    
    producer.on('producer.connect', () => {
      kafkaLogger.info('Kafka Producer连接成功')
    })
    
    producer.on('producer.disconnect', () => {
      kafkaLogger.warn('Kafka Producer连接断开')
    })
    
    producer.on('producer.network.request_timeout', (payload) => {
      kafkaLogger.error('Kafka Producer请求超时', payload)
    })
    
    await producer.connect()
    kafkaLogger.info('Kafka Producer初始化完成')
  }
  
  return producer
}

/**
 * 获取Consumer实例
 */
export async function getConsumer(): Promise<Consumer> {
  if (!consumer) {
    consumer = kafka.consumer(consumerConfig)
    
    consumer.on('consumer.connect', () => {
      kafkaLogger.info('Kafka Consumer连接成功')
    })
    
    consumer.on('consumer.disconnect', () => {
      kafkaLogger.warn('Kafka Consumer连接断开')
    })
    
    consumer.on('consumer.crash', (payload) => {
      kafkaLogger.error('Kafka Consumer崩溃', payload)
    })
    
    await consumer.connect()
    kafkaLogger.info('Kafka Consumer初始化完成')
  }
  
  return consumer
}

/**
 * 发送消息到Kafka
 */
export async function sendMessage(
  topic: TopicName,
  message: any,
  key?: string,
  partition?: number
): Promise<void> {
  try {
    const producer = await getProducer()
    
    const messagePayload = {
      topic,
      messages: [{
        key: key || null,
        value: JSON.stringify(message),
        partition: partition || undefined,
        timestamp: Date.now().toString(),
      }],
    }
    
    const result = await producer.send(messagePayload)
    
    kafkaLogger.info('消息发送成功', {
      topic,
      key,
      partition,
      result,
    })
  } catch (error) {
    kafkaLogger.error('消息发送失败', {
      topic,
      key,
      error: error.message,
    })
    throw error
  }
}

/**
 * 批量发送消息到Kafka
 */
export async function sendBatchMessages(
  topic: TopicName,
  messages: Array<{ key?: string; value: any; partition?: number }>
): Promise<void> {
  try {
    const producer = await getProducer()
    
    const messagePayload = {
      topic,
      messages: messages.map(msg => ({
        key: msg.key || null,
        value: JSON.stringify(msg.value),
        partition: msg.partition || undefined,
        timestamp: Date.now().toString(),
      })),
    }
    
    const result = await producer.send(messagePayload)
    
    kafkaLogger.info('批量消息发送成功', {
      topic,
      messageCount: messages.length,
      result,
    })
  } catch (error) {
    kafkaLogger.error('批量消息发送失败', {
      topic,
      messageCount: messages.length,
      error: error.message,
    })
    throw error
  }
}

/**
 * 订阅主题并处理消息
 */
export async function subscribeToTopic(
  topic: TopicName,
  messageHandler: (payload: EachMessagePayload) => Promise<void>
): Promise<void> {
  try {
    const consumer = await getConsumer()
    
    await consumer.subscribe({ topic, fromBeginning: false })
    
    kafkaLogger.info('订阅主题成功', { topic })
    
    await consumer.run({
      eachMessage: async (payload) => {
        const { topic, partition, message } = payload
        
        kafkaLogger.debug('收到消息', {
          topic,
          partition,
          offset: message.offset,
          key: message.key?.toString(),
        })
        
        try {
          await messageHandler(payload)
          
          kafkaLogger.debug('消息处理成功', {
            topic,
            partition,
            offset: message.offset,
          })
        } catch (error) {
          kafkaLogger.error('消息处理失败', {
            topic,
            partition,
            offset: message.offset,
            error: error.message,
          })
          
          // 这里可以添加错误处理逻辑，比如发送到死信队列
          throw error
        }
      },
    })
  } catch (error) {
    kafkaLogger.error('订阅主题失败', {
      topic,
      error: error.message,
    })
    throw error
  }
}

/**
 * 关闭Kafka连接
 */
export async function closeKafka(): Promise<void> {
  try {
    if (producer) {
      await producer.disconnect()
      producer = null
      kafkaLogger.info('Kafka Producer连接已关闭')
    }
    
    if (consumer) {
      await consumer.disconnect()
      consumer = null
      kafkaLogger.info('Kafka Consumer连接已关闭')
    }
  } catch (error) {
    kafkaLogger.error('关闭Kafka连接失败', { error: error.message })
    throw error
  }
}

// 导出主题常量
export { topics }
