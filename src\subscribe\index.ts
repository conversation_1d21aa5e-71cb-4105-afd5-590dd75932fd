import { z } from '@hono/zod-openapi'

import { and, eq } from 'drizzle-orm'
import { sendBatchSubscribeMessagesSingle } from '@/services/douyin-message'
import { getDb } from '../db'
import { userSubscribe, userSubscribeInsertSchema } from '../schema'
import { randomUUID } from 'crypto';
import { getChinaTime } from '@/utils/time'
const subscribeMessageSchema = z.object({
  openId: z.string().min(1),
  platform: z.enum(['dy', 'kuaishou', 'bl', 'wechat', 'baidu']),
  subscribeResult:z.array(z.object({
    tplId: z.string().min(1),
    status: z.enum(['accept', 'reject', 'ban', 'fail']),
    count:z.number().default(0),
    lastReceivedAt: z.number().optional()
  })),
 
})

const subscribeMessageSingleSchema = z.object({
  appId: z.string(),
  page: z.string().optional(),
  messageData: z.record(z.string(), z.string()).optional(),
  tplId: z.string(),
  platform: z.string(),
  openId: z.string(),
  id: z.string(),
  count: z.number(),
})

export async function handleSubscribe(c: any) {
  const body = await c.req.json()
  const parse = subscribeMessageSchema.safeParse(body)
  if (!parse.success) {
    return c.json({ error: '参数校验失败' }, 400)
  }
  const { openId, subscribeResult, platform} = parse.data
  // const tplIds = subscribeResult.map(i=>i.tplId)
  const db = await getDb()
  if (!db)
    return c.json({ error: '数据库未初始化' }, 500)
  const now = getChinaTime()
  for (const subscribe of subscribeResult) {
    const status = subscribe.status
    const count = subscribe.count
    const tplId = subscribe.tplId
    const lastReceivedAt = subscribe.lastReceivedAt

    // 先查询是否存在相同的 openId 和 tplId 组合
    const existingRecord = await db
      .select()
      .from(userSubscribe)
      .where(and(eq(userSubscribe.openId, openId), eq(userSubscribe.tplId, tplId)))
      .limit(1)
      .execute()

    if (existingRecord.length > 0) {
      // 如果记录存在，执行更新
      const updateDataInput: any = {
        status,
        count,
        updatedAt: now,
        platform
      }

      // 如果提供了 lastReceivedAt，则更新该字段
      if (lastReceivedAt) {
        updateDataInput.lastReceivedAt = new Date(lastReceivedAt)
      }

      const updateData = userSubscribeInsertSchema.safeParse(updateDataInput)

      if (!updateData.success) {
        return c.json({ error: '更新数据校验失败' }, 400)
      }

      await db
        .update(userSubscribe)
        .set(updateData.data)
        .where(and(eq(userSubscribe.openId, openId), eq(userSubscribe.tplId, tplId)))
        .execute()
        return c.json({ msg:"",type:"update",subscribeResult}, 200)
    } else {
      // 如果记录不存在，执行插入
      const id = randomUUID()
      const insertData: any = {
        openId,
        tplId,
        status,
        count,
        updatedAt: now,
        platform,
        id,
        createdAt: now
      }

      // 如果提供了 lastReceivedAt，则设置该字段
      if (lastReceivedAt) {
        insertData.lastReceivedAt = getChinaTime(new Date(lastReceivedAt)) 
      }

      await db
        .insert(userSubscribe)
        .values(insertData)
        .execute()
        return c.json({ msg:"",type:"insert",subscribeResult}, 200)

    }
  }
}

export async function execSubscribe(c: any) {
  const body = await c.req.json()
  const parse = subscribeMessageSingleSchema.safeParse(body)
  if (!parse.success) {
    return c.json({ msg: '参数校验失败' }, 400)
  }
  const { appId, page, messageData, tplId, platform, openId, id, count } = parse.data

  const info = await sendBatchSubscribeMessagesSingle({ appId, page, messageData, tplId, platform }, { openId, id, count })
  return c.json(info, 200)
}
