@echo off
echo ========================================
echo 零停机部署脚本
echo ========================================

REM 设置变量
set OLD_CONTAINER=nqhy-backend
set NEW_CONTAINER=nqhy-backend-new
set IMAGE_NAME=nqhy-backend:latest

echo 1. 构建新镜像...
docker-compose build --no-cache
if %errorlevel% neq 0 (
    echo ERROR: 镜像构建失败
    pause
    exit /b 1
)

echo 2. 启动新容器（使用不同端口）...
docker run -d ^
    --name %NEW_CONTAINER% ^
    --env-file .env.production.local ^
    -p 3001:3000 ^
    --network nqhy-backend_nqhy-network ^
    %IMAGE_NAME%

if %errorlevel% neq 0 (
    echo ERROR: 新容器启动失败
    pause
    exit /b 1
)

echo 3. 等待新容器健康检查...
:wait_loop
timeout /t 5 /nobreak >nul
curl -f http://localhost:3001/ >nul 2>&1
if %errorlevel% neq 0 (
    echo 等待新容器启动...
    goto wait_loop
)

echo 4. 新容器启动成功，停止旧容器...
docker stop %OLD_CONTAINER% >nul 2>&1
docker rm %OLD_CONTAINER% >nul 2>&1

echo 5. 将新容器端口切换到3000...
docker stop %NEW_CONTAINER%
docker rm %NEW_CONTAINER%

echo 6. 启动最终容器...
docker-compose up -d

echo ========================================
echo 零停机部署完成！
echo ========================================
pause
